"""add_status_deadline_to_jobs_and_is_blocklisted_to_candidates

Revision ID: 6e3fb122d042
Revises: 74064995ada4
Create Date: 2025-09-21 14:33:28.465041

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6e3fb122d042'
down_revision: Union[str, None] = '74064995ada4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Add is_blocklisted column with default value for existing records
    op.add_column('t_candidates', sa.Column('is_blocklisted', sa.<PERSON>(), nullable=False, server_default='false', comment='Whether the candidate is blocklisted'))

    # Add status column with default value for existing records
    op.add_column('t_jobs', sa.Column('status', sa.Integer(), nullable=False, server_default='1', comment='1: Draft, 3: Open, 5: Closed, 7: Cancelled'))

    # Add application_deadline column (nullable, so no default needed)
    op.add_column('t_jobs', sa.Column('application_deadline', sa.DateTime(timezone=True), nullable=True, comment='Deadline for job applications'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('t_jobs', 'application_deadline')
    op.drop_column('t_jobs', 'status')
    op.drop_column('t_candidates', 'is_blocklisted')
    # ### end Alembic commands ###
