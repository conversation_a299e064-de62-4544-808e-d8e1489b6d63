"""add_job_role_id_to_job_requirements_and_create_job_requirement_skills_table

Revision ID: 74064995ada4
Revises: c45d1c3adbf6
Create Date: 2025-09-21 14:28:31.278549

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '74064995ada4'
down_revision: Union[str, None] = 'c45d1c3adbf6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('t_job_requirement_skills',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('job_requirement_id', sa.Integer(), nullable=False),
    sa.Column('skill_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['job_requirement_id'], ['t_job_requirements.id'], ),
    sa.ForeignKeyConstraint(['skill_id'], ['m_skills.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('t_job_requirements', sa.Column('job_role_id', sa.Integer(), nullable=True, comment='Reference to job role master data'))
    op.create_foreign_key(None, 't_job_requirements', 'm_job_roles', ['job_role_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 't_job_requirements', type_='foreignkey')
    op.drop_column('t_job_requirements', 'job_role_id')
    op.drop_table('t_job_requirement_skills')
    # ### end Alembic commands ###
